<script>
	import { browser } from '$app/environment';
	import DynamicTitle from '$lib/components/DynamicTitle.svelte';
	import EmailSignup from '$lib/components/EmailSignup.svelte';

	const features = [
		{
			name: 'Unified Search',
			description:
				'Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more from one place.',
			icon: '/magnifying-glass-tilted-left_1f50d.png'
		},
		{
			name: 'Centralized Lists',
			description: 'Manage your watch, read, and play lists for all services seamlessly.',
			icon: '/books_1f4da.png'
		},
		{
			name: 'Track Friends Aniwhere',
			description:
				"See what your friends are watching and reading across all platforms with real-time activity feeds.",
			icon: '/busts-in-silhouette_1f465.png'
		}
	];
	$: title = 'Home - Anithing.moe';
	$: description =
		'Anithing.moe is your central hub for Japanese media. Search anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB, and more. Manage your lists and track friends activities seamlessly.';
	$: imageUrl = browser ? `${window.location.origin}/api/og?type=static` : 'https://anithing.moe/api/og?type=static';
	$: canonicalUrl = browser ? window.location.href : 'https://anithing.moe/';
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={imageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content="anithing.moe - Unified Japanese Media Hub" />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="website" />
	<meta property="og:site_name" content="anithing.moe" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:site" content="anithing_moe" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={imageUrl} />
	<meta name="twitter:image:alt" content="anithing.moe - Unified Japanese Media Hub" />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta
		name="keywords"
		content="anime, manga, visual novel, light novel, MAL, AniList, Kitsu, VNDB, unified search, media tracking, anithing, anithing.moe, japanese media"
	/>
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content="Anithing.moe" />
	<meta name="robots" content="index, follow" />
</svelte:head>

<div
	class="flex min-h-screen flex-col items-center justify-center px-3 py-10 sm:px-6 sm:py-16 lg:px-8"
>
	<header class="mb-8 sm:mb-10 md:mb-16">
		<div class="flex flex-col items-center justify-center">
			<DynamicTitle />
			<p
				class="mx-auto mt-3 max-w-3xl px-1 text-center text-lg text-slate-300 sm:mt-4 sm:text-xl md:text-2xl lg:text-3xl"
			>
				Search, track, and manage all your lists in one unified experience.
			</p>
		</div>
	</header>

	<main class="w-full max-w-4xl">
		<!-- Features Section -->
		<section class="mb-12 md:mb-16">
			<!-- Mobile/Tablet: Vertical layout with icons on left -->
			<div class="flex flex-col gap-6 sm:gap-8 md:hidden">
				{#each features as feature}
					<div class="flex items-start gap-4 sm:gap-6 rounded-xl border border-gray-700/50 bg-gray-800/50 p-4 shadow-lg backdrop-blur-sm sm:p-6">
						<!-- Icon on the left -->
						<div class="flex-shrink-0">
							<div class="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-sky-500/20 flex items-center justify-center">
								<img src={feature.icon} alt={feature.name} class="w-6 h-6 sm:w-8 sm:h-8" />
							</div>
						</div>
						<!-- Content on the right -->
						<div class="flex-1 min-w-0">
							<h3 class="mb-2 text-lg font-semibold text-sky-300 sm:text-xl">{feature.name}</h3>
							<p class="text-sm text-slate-300 sm:text-base leading-relaxed">{feature.description}</p>
						</div>
					</div>
				{/each}
			</div>

			<!-- Desktop: 3-column grid layout -->
			<div class="hidden md:grid md:grid-cols-3 md:gap-6 lg:gap-8">
				{#each features as feature}
					<div class="rounded-xl bg-gray-800/60 p-6 border border-gray-700/30">
						<!-- Icon at top -->
						<div class="mb-4 flex justify-center">
							<div class="w-12 h-12 lg:w-16 lg:h-16 rounded-full bg-sky-500/20 flex items-center justify-center">
								<img src={feature.icon} alt={feature.name} class="w-6 h-6 lg:w-8 lg:h-8" />
							</div>
						</div>
						<!-- Content below -->
						<div class="text-center">
							<h3 class="mb-3 text-lg font-semibold text-sky-300 lg:text-xl">{feature.name}</h3>
							<p class="text-sm text-slate-300 lg:text-base leading-relaxed">{feature.description}</p>
						</div>
					</div>
				{/each}
			</div>
		</section>

		<!-- Email Signup Section -->
		<section class="flex flex-col items-center">
			<EmailSignup />
		</section>
	</main>

	<footer class="mt-16 text-center text-sm text-slate-400">
		<p>© {new Date().getFullYear()} Anithing.moe - All your media, aniwhere.</p>
	</footer>
</div>